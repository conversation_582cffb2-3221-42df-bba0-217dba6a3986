package com.trading.financialindicatordaemon.service.mining;

import com.trading.financialindicatordaemon.amqp.publisher.RabbitMqPublisher;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

@Service
public class ScheduledMiningService {

    private final RabbitMqPublisher rabbitMqPublisher;

    public ScheduledMiningService(RabbitMqPublisher rabbitMqPublisher) {
        this.rabbitMqPublisher = rabbitMqPublisher;
    }

    @Scheduled(cron = "0 */5 3-13 * * *")
    public void triggerCryptoMining() {
        rabbitMqPublisher.publishMineActiveSymbols();
    }

    @Scheduled(cron = "0 */7 14-23 * * *")
    public void triggerStockMining() {
        rabbitMqPublisher.publishMineActiveStockSymbols();
    }

}
