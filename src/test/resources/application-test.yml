spring:
  liquibase:
    enabled: true
    change-log: classpath:db/changelog/db.changelog-master.sql
    default-schema: public

  rabbitmq:
    host: localhost
    port: 5672
    username: guest
    password: guest

# Test configuration
app:
  cmc:
    throttle:
      min: 100
      max: 200
  indicator-api:
    host: http://localhost:8080
  amount-of-coins-to-get-by-ranking: 3

logging:
  level:
    com.trading.financialindicatordaemon: DEBUG
    com.trading.financialindicatordaemon.client: WARN
    org.springframework.amqp: WARN
    feign: WARN
